# LangGraph 图服务使用指南

## 🎯 概述

本指南介绍如何使用已配置的两个图服务：`designToCode` 和 `simpleChat`。

## 🚀 服务架构

```
用户请求 → HTTP代理(8000) → HTTP服务(5557) → 图服务(5556) → Python后端(5555) → PostgreSQL
```

## 📋 可用的图

### 1. designToCode
- **功能**: 设计转代码
- **路径**: `/Users/<USER>/projs/ht/langgraph-mvp-demo/dist/graph/designToCode/index.js:graph`
- **用途**: 将设计描述转换为代码实现

### 2. simpleChat  
- **功能**: 简单聊天
- **路径**: `/Users/<USER>/projs/ht/langgraph-mvp-demo/dist/graph/simpleChat/index.js:graph`
- **用途**: 基础对话交互

## 🔧 API 端点

### 基础端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/ok` | GET | 健康检查 |
| `/{graphId}/getSchema` | POST | 获取图结构 |
| `/{graphId}/getGraph` | POST | 获取图定义 |
| `/{graphId}/streamEvents` | POST | 流式执行图 |
| `/{graphId}/getState` | POST | 获取图状态 |
| `/{graphId}/updateState` | POST | 更新图状态 |

### 图特定端点

#### designToCode 图
```bash
# 获取图结构
curl -X POST http://localhost:5556/designToCode/getSchema \
  -H "Content-Type: application/json" \
  -d '{}'

# 流式执行
curl -X POST http://localhost:5556/designToCode/streamEvents \
  -H "Content-Type: application/json" \
  -d '{
    "input": {
      "design_description": "创建一个简单的登录页面"
    },
    "config": {
      "configurable": {
        "thread_id": "design-session-001"
      }
    },
    "stream_mode": ["updates"]
  }'
```

#### simpleChat 图
```bash
# 获取图结构
curl -X POST http://localhost:5556/simpleChat/getSchema \
  -H "Content-Type: application/json" \
  -d '{}'

# 流式聊天
curl -X POST http://localhost:5556/simpleChat/streamEvents \
  -H "Content-Type: application/json" \
  -d '{
    "input": {
      "message": "你好，请介绍一下自己"
    },
    "config": {
      "configurable": {
        "thread_id": "chat-session-001"
      }
    },
    "stream_mode": ["updates", "messages"]
  }'
```

## 🧪 测试脚本

### 1. 基础连通性测试

```bash
#!/bin/bash

echo "🧪 测试图服务连通性..."

# 测试健康检查
echo "1. 测试健康检查"
curl -s http://localhost:5556/ok

# 测试图列表
echo -e "\n2. 测试可用图"
for graph in designToCode simpleChat; do
    echo "测试图: $graph"
    response=$(curl -s -X POST http://localhost:5556/$graph/getSchema \
        -H "Content-Type: application/json" \
        -d '{}')
    
    if [[ "$response" == *"schema"* ]] || [[ "$response" == *"title"* ]]; then
        echo "✅ $graph - 可用"
    else
        echo "❌ $graph - 不可用: $response"
    fi
done
```

### 2. 图执行测试

```bash
#!/bin/bash

echo "🚀 测试图执行..."

# 测试 simpleChat
echo "1. 测试 simpleChat 图"
curl -X POST http://localhost:5556/simpleChat/streamEvents \
  -H "Content-Type: application/json" \
  -d '{
    "input": {
      "message": "Hello, world!"
    },
    "config": {
      "configurable": {
        "thread_id": "test-chat-001"
      }
    },
    "stream_mode": ["updates"]
  }'

echo -e "\n\n2. 测试 designToCode 图"
curl -X POST http://localhost:5556/designToCode/streamEvents \
  -H "Content-Type: application/json" \
  -d '{
    "input": {
      "design_description": "Create a simple button component"
    },
    "config": {
      "configurable": {
        "thread_id": "test-design-001"
      }
    },
    "stream_mode": ["updates"]
  }'
```

## 📊 状态管理

### 获取图状态
```bash
curl -X POST http://localhost:5556/{graphId}/getState \
  -H "Content-Type: application/json" \
  -d '{
    "config": {
      "configurable": {
        "thread_id": "your-thread-id"
      }
    }
  }'
```

### 更新图状态
```bash
curl -X POST http://localhost:5556/{graphId}/updateState \
  -H "Content-Type: application/json" \
  -d '{
    "config": {
      "configurable": {
        "thread_id": "your-thread-id"
      }
    },
    "values": {
      "key": "value"
    }
  }'
```

## 🔍 调试和监控

### 查看日志
```bash
# 图服务日志
tail -f logs/graph_service.log

# HTTP服务日志
tail -f logs/http_service.log

# Python后端日志
tail -f logs/python_service.log
```

### 检查服务状态
```bash
# 检查端口占用
lsof -i :5555  # Python后端
lsof -i :5556  # 图服务
lsof -i :5557  # HTTP服务
lsof -i :8000  # 代理服务

# 检查进程
ps aux | grep -E "(python_service|tsx|node)" | grep -v grep
```

## ⚠️ 常见问题

### 1. 图文件不存在
**错误**: `Failed to load graph: ENOENT`
**解决**: 确保外部项目已构建，文件路径正确

### 2. 图加载失败
**错误**: `Cannot resolve graph`
**解决**: 检查图文件的导出格式，确保导出了正确的图对象

### 3. 数据库连接失败
**错误**: `Database connection failed`
**解决**: 检查PostgreSQL服务状态和连接参数

### 4. 端口冲突
**错误**: `EADDRINUSE`
**解决**: 使用 `./stop_services.sh` 停止服务，或手动杀死占用端口的进程

## 🎨 自定义图开发

如果需要添加新的图：

1. **创建图文件**
```typescript
// src/graphs/my_graph.ts
import { StateGraph } from "@langchain/langgraph";

interface MyState {
  input: string;
  output: string;
}

const graph = new StateGraph<MyState>({...});
// 添加节点和边...

export const compiledGraph = graph.compile();
export default compiledGraph;
```

2. **更新环境变量**
```bash
export LANGSERVE_GRAPHS='{"myGraph": "src/graphs/my_graph.ts:compiledGraph"}'
```

3. **重启服务**
```bash
./stop_services.sh
./start_services.sh
```

## 📚 更多资源

- [LangGraph 官方文档](https://langchain-ai.github.io/langgraph/)
- [API 参考](https://langchain-ai.github.io/langgraph/reference/)
- [示例代码](https://github.com/langchain-ai/langgraph/tree/main/examples)
