#!/usr/bin/env node

/**
 * HTTP代理服务
 * 将端口8000的请求代理到内部的5557端口
 * 解决client.http.mts端口配置问题
 */

const http = require('http');
const httpProxy = require('http-proxy');

const TARGET_PORT = 5557; // client.http.mts实际监听的端口
const PROXY_PORT = process.env.PORT || 8000; // 对外暴露的端口

// 创建代理服务器
const proxy = httpProxy.createProxyServer({
  target: `http://localhost:${TARGET_PORT}`,
  changeOrigin: true,
  timeout: 30000,
  proxyTimeout: 30000,
});

// 创建HTTP服务器
const server = http.createServer((req, res) => {
  // 添加CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  // 处理OPTIONS预检请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  // 代理请求
  proxy.web(req, res, (err) => {
    console.error('代理错误:', err.message);
    
    if (!res.headersSent) {
      res.writeHead(502, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        error: 'Bad Gateway',
        message: `无法连接到后端服务 (端口${TARGET_PORT})`,
        details: err.message
      }));
    }
  });
});

// 处理代理错误
proxy.on('error', (err, req, res) => {
  console.error('代理服务错误:', err.message);
});

// 启动服务器
server.listen(PROXY_PORT, 'localhost', () => {
  console.log(`🌐 HTTP代理服务启动成功`);
  console.log(`   对外端口: ${PROXY_PORT}`);
  console.log(`   目标端口: ${TARGET_PORT}`);
  console.log(`   访问地址: http://localhost:${PROXY_PORT}`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭代理服务...');
  server.close(() => {
    console.log('代理服务已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭代理服务...');
  server.close(() => {
    console.log('代理服务已关闭');
    process.exit(0);
  });
});
