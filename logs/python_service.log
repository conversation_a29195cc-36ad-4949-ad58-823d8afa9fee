INFO:     Started server process [75958]
INFO:     Waiting for application startup.
INFO:python_service:连接数据库: postgresql://postgres:12345678@localhost:5432/postgres
INFO:python_service:数据库连接池创建成功
INFO:python_service:数据库表结构初始化完成
INFO:     Application startup complete.
ERROR:    [Errno 48] error while attempting to bind on address ('0.0.0.0', 5555): address already in use
INFO:     Waiting for application shutdown.
INFO:python_service:数据库连接池已关闭
INFO:     Application shutdown complete.
启动Python服务，数据库连接: postgresql://postgres:12345678@localhost:5432/postgres
服务端口: 5555
                                                                                                                                                                                                                                           INFO:     127.0.0.1:65029 - "GET /ok HTTP/1.1" 200 OK
INFO:     127.0.0.1:65076 - "GET /ok HTTP/1.1" 200 OK
