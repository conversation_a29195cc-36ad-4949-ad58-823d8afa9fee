node:events:502
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use ::1:8000
    at Server.setupListenHandle [as _listen2] (node:net:1908:16)
    at listenInCluster (node:ne代理错误: 
代理错误: 
代理错误: 
代理错误: 
代理错误: 
7)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:111:8)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1944:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21) {
  code: 'EADDRINUSE',
  errno: -48,
  syscall: 'listen',
  address: '::1',
  port: 8000
}

Node.js v20.19.0
