{"timestamp":"2025-08-26T09:13:03.184Z","level":"info","event":"Starting HTTP loop","pid":76118}
{"timestamp":"2025-08-26T09:13:03.204Z","level":"error","event":"Error: Failed to load HTTP app: /Users/<USER>/projs/ht/langgraph-mvp-demo/dist/routes/app.js\n    at registerHttp (/Users/<USER>/projs/langgraph-api-js/client.http.mts:102:20)\n    at async main (/Users/<USER>/projs/langgraph-api-js/client.http.mts:138:15)","error":{},"stack":"Error: Failed to load HTTP app: /Users/<USER>/projs/ht/langgraph-mvp-demo/dist/routes/app.js\n    at registerHttp (/Users/<USER>/projs/langgraph-api-js/client.http.mts:102:20)\n    at async main (/Users/<USER>/projs/langgraph-api-js/client.http.mts:138:15)","rejection":true,"date":"Tue Aug 26 2025 17:13:03 GMT+0800 (China Standard Time)","process":{"pid":76118,"uid":501,"gid":20,"cwd":"/Users/<USER>/projs/langgraph-api-js","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","version":"v20.19.0","argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/projs/langgraph-api-js/client.http.mts"],"memoryUsage":{"rss":107151360,"heapTotal":20250624,"heapUsed":12333240,"external":3885414,"arrayBuffers":74178}},"os":{"loadavg":[3.89306640625,3.166015625,3.04443359375],"uptime":16084},"trace":[{"column":20,"file":"/Users/<USER>/projs/langgraph-api-js/client.http.mts","function":"registerHttp","line":102,"method":null,"native":false},{"column":15,"file":"/Users/<USER>/projs/langgraph-api-js/client.http.mts","function":"async main","line":138,"method":null,"native":false}],"message":"unhandledRejection: Failed to load HTTP app: /Users/<USER>/projs/ht/langgraph-mvp-demo/dist/routes/app.js\nError: Failed to load HTTP app: /Users/<USER>/projs/ht/langgraph-mvp-demo/dist/routes/app.js\n    at registerHttp (/Users/<USER>/projs/langgraph-api-js/client.http.mts:102:20)\n    at async main (/Users/<USER>/projs/langgraph-api-js/client.http.mts:138:15)"}
