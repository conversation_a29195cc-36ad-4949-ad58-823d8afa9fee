{"timestamp":"2025-08-26T09:13:03.455Z","level":"info","event":"Starting graph loop","pid":76117}
{"timestamp":"2025-08-26T09:13:03.458Z","level":"info","event":"Resolving graph designToCode"}
{"timestamp":"2025-08-26T09:13:03.459Z","level":"info","event":"Resolving graph simpleChat"}
SimpleChat: 图已创建并编译完成
{"timestamp":"2025-08-26T09:13:03.869Z","level":"error","event":"Error: listen EADDRINUSE: address already in use ::1:5556\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at GetAddrInfoReqWrap.doListen [as callback] (node:net:2139:7)\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:111:8)","code":"EADDRINUSE","errno":-48,"syscall":"listen","address":"::1","port":5556,"stack":"Error: listen EADDRINUSE: address already in use ::1:5556\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at GetAddrInfoReqWrap.doListen [as callback] (node:net:2139:7)\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:111:8)","message":"listen EADDRINUSE: address already in use ::1:5556"}
{"timestamp":"2025-08-26T09:13:03.870Z","level":"error","event":"Error: listen EADDRINUSE: address already in use ::1:5556\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at GetAddrInfoReqWrap.doListen [as callback] (node:net:2139:7)\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:111:8)","error":{"code":"EADDRINUSE","errno":-48,"syscall":"listen","address":"::1","port":5556,"level":"error"},"stack":"Error: listen EADDRINUSE: address already in use ::1:5556\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at GetAddrInfoReqWrap.doListen [as callback] (node:net:2139:7)\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:111:8)","exception":true,"date":"Tue Aug 26 2025 17:13:03 GMT+0800 (China Standard Time)","process":{"pid":76117,"uid":501,"gid":20,"cwd":"/Users/<USER>/projs/langgraph-api-js","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","version":"v20.19.0","argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/projs/langgraph-api-js/client.mts"],"memoryUsage":{"rss":192757760,"heapTotal":68190208,"heapUsed":36317624,"external":4551356,"arrayBuffers":148753}},"os":{"loadavg":[3.6611328125,3.1298828125,3.0322265625],"uptime":16084},"trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1908,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1965,"method":null,"native":false},{"column":7,"file":"node:net","function":"GetAddrInfoReqWrap.doListen [as callback]","line":2139,"method":"doListen [as callback]","native":false},{"column":8,"file":"node:dns","function":"GetAddrInfoReqWrap.onlookup [as oncomplete]","line":111,"method":"onlookup [as oncomplete]","native":false}],"message":"uncaughtException: listen EADDRINUSE: address already in use ::1:5556\nError: listen EADDRINUSE: address already in use ::1:5556\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at GetAddrInfoReqWrap.doListen [as callback] (node:net:2139:7)\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:111:8)"}
