#!/bin/bash

# 快速测试脚本 - 验证所有服务和图功能

echo "🧪 LangGraph API 快速测试"
echo "=========================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_endpoint() {
    local name="$1"
    local url="$2"
    local expected="$3"
    
    echo -n "测试 $name ... "
    
    response=$(curl -s "$url" 2>/dev/null)
    if [[ "$response" == *"$expected"* ]]; then
        echo -e "${GREEN}✅ 通过${NC}"
        return 0
    else
        echo -e "${RED}❌ 失败${NC}"
        echo "  响应: $response"
        return 1
    fi
}

test_post_endpoint() {
    local name="$1"
    local url="$2"
    local data="$3"
    local expected="$4"
    
    echo -n "测试 $name ... "
    
    response=$(curl -s -X POST "$url" \
        -H "Content-Type: application/json" \
        -d "$data" 2>/dev/null)
    
    if [[ "$response" == *"$expected"* ]]; then
        echo -e "${GREEN}✅ 通过${NC}"
        return 0
    else
        echo -e "${RED}❌ 失败${NC}"
        echo "  响应: $response"
        return 1
    fi
}

echo ""
echo "🔍 1. 基础服务健康检查"
echo "----------------------"

# 测试Python后端
test_endpoint "Python后端服务" "http://localhost:5555/ok" "ok"

# 测试图服务
test_endpoint "图服务" "http://localhost:5556/ok" "ok"

# 测试HTTP代理
test_endpoint "HTTP代理服务" "http://localhost:8000/health" ""

echo ""
echo "📊 2. 图结构查询测试"
echo "-------------------"

# 测试designToCode图结构
test_post_endpoint "designToCode图结构" \
    "http://localhost:5556/designToCode/getSchema" \
    '{}' \
    ""

# 测试simpleChat图结构
test_post_endpoint "simpleChat图结构" \
    "http://localhost:5556/simpleChat/getSchema" \
    '{}' \
    ""

echo ""
echo "🚀 3. 图执行测试"
echo "---------------"

# 测试simpleChat执行
echo "测试 simpleChat 图执行 ..."
response=$(curl -s -X POST http://localhost:5556/simpleChat/streamEvents \
    -H "Content-Type: application/json" \
    -d '{
        "input": {
            "message": "Hello, test!"
        },
        "config": {
            "configurable": {
                "thread_id": "test-chat-001"
            }
        },
        "stream_mode": ["updates"]
    }' 2>/dev/null)

if [[ "$response" == *"event"* ]] || [[ "$response" == *"data"* ]] || [[ "$response" != "" ]]; then
    echo -e "${GREEN}✅ simpleChat 执行成功${NC}"
else
    echo -e "${RED}❌ simpleChat 执行失败${NC}"
    echo "  响应: $response"
fi

# 测试designToCode执行
echo "测试 designToCode 图执行 ..."
response=$(curl -s -X POST http://localhost:5556/designToCode/streamEvents \
    -H "Content-Type: application/json" \
    -d '{
        "input": {
            "design_description": "Create a simple test component"
        },
        "config": {
            "configurable": {
                "thread_id": "test-design-001"
            }
        },
        "stream_mode": ["updates"]
    }' 2>/dev/null)

if [[ "$response" == *"event"* ]] || [[ "$response" == *"data"* ]] || [[ "$response" != "" ]]; then
    echo -e "${GREEN}✅ designToCode 执行成功${NC}"
else
    echo -e "${RED}❌ designToCode 执行失败${NC}"
    echo "  响应: $response"
fi

echo ""
echo "💾 4. 数据库功能测试"
echo "------------------"

# 测试checkpointer
echo "测试 checkpointer 功能 ..."
response=$(curl -s -X POST http://localhost:5555/checkpointer_put \
    -H "Content-Type: application/json" \
    -d '{
        "config": {
            "configurable": {
                "thread_id": "test-thread-001",
                "checkpoint_id": "test-checkpoint-001"
            }
        },
        "checkpoint": {
            "test": "data",
            "timestamp": "2024-01-01T00:00:00Z"
        },
        "metadata": {
            "source": "quick_test"
        }
    }' 2>/dev/null)

if [[ "$response" == *"configurable"* ]]; then
    echo -e "${GREEN}✅ Checkpointer 保存成功${NC}"
else
    echo -e "${RED}❌ Checkpointer 保存失败${NC}"
    echo "  响应: $response"
fi

# 测试store
echo "测试 store 功能 ..."
response=$(curl -s -X POST http://localhost:5555/store_put \
    -H "Content-Type: application/json" \
    -d '{
        "namespace": ["test", "quick"],
        "key": "test-key",
        "value": {
            "message": "Quick test data",
            "timestamp": "2024-01-01T00:00:00Z"
        }
    }' 2>/dev/null)

if [[ "$response" == *"success"* ]]; then
    echo -e "${GREEN}✅ Store 保存成功${NC}"
else
    echo -e "${RED}❌ Store 保存失败${NC}"
    echo "  响应: $response"
fi

echo ""
echo "📋 5. 测试总结"
echo "============="

echo ""
echo -e "${YELLOW}🔗 可用的服务端点:${NC}"
echo "  Python后端: http://localhost:5555"
echo "  图服务: http://localhost:5556"
echo "  HTTP API: http://localhost:8000"
echo ""
echo -e "${YELLOW}📊 可用的图:${NC}"
echo "  designToCode: http://localhost:5556/designToCode/*"
echo "  simpleChat: http://localhost:5556/simpleChat/*"
echo ""
echo -e "${YELLOW}💡 使用示例:${NC}"
echo "  # 获取图结构"
echo "  curl -X POST http://localhost:5556/simpleChat/getSchema -H 'Content-Type: application/json' -d '{}'"
echo ""
echo "  # 执行图"
echo "  curl -X POST http://localhost:5556/simpleChat/streamEvents \\"
echo "    -H 'Content-Type: application/json' \\"
echo "    -d '{\"input\": {\"message\": \"Hello!\"}, \"config\": {\"configurable\": {\"thread_id\": \"test\"}}, \"stream_mode\": [\"updates\"]}'"
echo ""
echo "测试完成! 🎉"
